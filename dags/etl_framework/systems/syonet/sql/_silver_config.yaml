# Configuração Silver - Sistema syonet
# 
# IMPORTANTE: Apenas dependências entre transformações SILVER são necessárias.
# Dependências BRONZE são implícitas (silver sempre roda após bronze).
# 
# NOVO: Parâmetros de criação de tabela são gerados automaticamente
# - table_name: Gerado como "silver_{system}_{transformation_name}"
# - drop_if_exists: true por padrão
# - grants: Lista de usuários para GRANT SELECT

transformations:
  # Transformações base - sem dependências silver
  tb_oportunidades_base:
    frequency: daily
    timeout_seconds: 900
    description: "Tabela consolidada de oportunidades do Syonet com dados enriquecidos (OTIMIZADA)"
    tags: ["syonet", "oportunidades", "vendas", "base"]
    drop_if_exists: true
    grants: ["bq_vitor_barros_u"]
    
  tb_IM_Assinatura_original:
    frequency: daily
    timeout_seconds: 900
    description: "Tabela de assinaturas e indicações do Syonet (OTIMIZADA)"
    tags: ["syonet", "assinaturas", "indicacoes"]
    drop_if_exists: true
    grants: ["bq_vitor_barros_u"]
    
  tb_maquinas_semana_passada:
    frequency: weekly
    timeout_seconds: 300
    description: "Relatório semanal de máquinas (executado apenas às sextas-feiras)"
    tags: ["syonet", "maquinas", "semanal"]
    drop_if_exists: true
    grants: ["bq_vitor_barros_u"]
    
  # Transformações com dependências silver
  tb_maquinas_atual:
    dependencies: [tb_oportunidades_base]  # Depende da silver oportunidades_base
    frequency: daily
    timeout_seconds: 300
    description: "Cópia atual da tabela de oportunidades para compatibilidade"
    tags: ["syonet", "maquinas", "compatibilidade"]
    drop_if_exists: true
    grants: ["bq_vitor_barros_u"]
    
  bi_maquinas:
    dependencies: [tb_maquinas_atual]  # Depende da silver maquinas_atual
    frequency: daily
    timeout_seconds: 600
    description: "View BIMaquinas migrada do SQL Server com classificação de clientes"
    tags: ["syonet", "bi", "maquinas", "classificacao"]
    drop_if_exists: true
    grants: ["bq_vitor_barros_u"]
    
  bi_maquinas_pivotada:
    dependencies: [bi_maquinas]  # Depende da silver bi_maquinas
    frequency: daily
    timeout_seconds: 600
    description: "View BIMaquinasPivotada com dados pivotados usando UNNEST PostgreSQL"
    tags: ["syonet", "bi", "maquinas", "pivotada"]
    drop_if_exists: true
    grants: ["bq_vitor_barros_u"]