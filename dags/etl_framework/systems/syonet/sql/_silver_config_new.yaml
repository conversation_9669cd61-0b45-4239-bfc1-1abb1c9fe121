# Configuração Silver - Sistema syonet (NOVA VERSÃO COM PERMISSÕES AUTOMÁTICAS)
# 
# IMPORTANTE: Apenas dependências entre transformações SILVER são necessárias.
# Dependências BRONZE são implícitas (silver sempre roda após bronze).
# 
# 🆕 NOVO: Permissões são aplicadas automaticamente da tabela aux_permissions_tables
# - Não é mais necessário especificar grants manualmente
# - O sistema busca automaticamente as permissões baseadas no nome da tabela
# - Mantém compatibilidade com grants manuais se necessário
# 
# PARÂMETROS AUTOMÁTICOS:
# - table_name: Gerado como "silver_{system}_{transformation_name}"
# - drop_if_exists: true por padrão
# - grants: Aplicados automaticamente da tabela aux_permissions_tables

transformations:
  # Transformações base - sem dependências silver
  tb_oportunidades_base:
    frequency: daily
    timeout_seconds: 900
    description: "Tabela consolidada de oportunidades do Syonet com dados enriquecidos (OTIMIZADA)"
    tags: ["syonet", "oportunidades", "vendas", "base"]
    drop_if_exists: true
    # 🆕 REMOVIDO: grants: ["bq_vitor_barros_u"] - Agora automático!
    
  tb_IM_Assinatura_original:
    frequency: daily
    timeout_seconds: 900
    description: "Tabela de assinaturas e indicações do Syonet (OTIMIZADA)"
    tags: ["syonet", "assinaturas", "indicacoes"]
    drop_if_exists: true
    # 🆕 REMOVIDO: grants: ["bq_vitor_barros_u"] - Agora automático!
    
  tb_maquinas_semana_passada:
    frequency: weekly
    timeout_seconds: 300
    description: "Relatório semanal de máquinas (executado apenas às sextas-feiras)"
    tags: ["syonet", "maquinas", "semanal"]
    drop_if_exists: true
    # 🆕 REMOVIDO: grants: ["bq_vitor_barros_u"] - Agora automático!
    
  # Transformações com dependências silver
  tb_maquinas_atual:
    dependencies: [tb_oportunidades_base]  # Depende da silver oportunidades_base
    frequency: daily
    timeout_seconds: 300
    description: "Cópia atual da tabela de oportunidades para compatibilidade"
    tags: ["syonet", "maquinas", "compatibilidade"]
    drop_if_exists: true
    # 🆕 REMOVIDO: grants: ["bq_vitor_barros_u"] - Agora automático!
    
  bi_maquinas:
    dependencies: [tb_maquinas_atual]  # Depende da silver maquinas_atual
    frequency: daily
    timeout_seconds: 600
    description: "View BIMaquinas migrada do SQL Server com classificação de clientes"
    tags: ["syonet", "bi", "maquinas", "classificacao"]
    drop_if_exists: true
    # 🆕 REMOVIDO: grants: ["bq_vitor_barros_u"] - Agora automático!
    
  bi_maquinas_pivotada:
    dependencies: [bi_maquinas]  # Depende da silver bi_maquinas
    frequency: daily
    timeout_seconds: 600
    description: "View BIMaquinasPivotada com dados pivotados usando UNNEST PostgreSQL"
    tags: ["syonet", "bi", "maquinas", "pivotada"]
    drop_if_exists: true
    # 🆕 REMOVIDO: grants: ["bq_vitor_barros_u"] - Agora automático!

# 🆕 EXEMPLO: Como manter grants manuais se necessário (para casos especiais)
# transformations:
#   exemplo_com_grants_especiais:
#     frequency: daily
#     description: "Exemplo com grants especiais além dos automáticos"
#     grants: ["usuario_especial", "GRANT ALL ON TABLE {table_name} TO admin_user"]
#     # Neste caso, tanto os grants automáticos quanto os manuais serão aplicados
