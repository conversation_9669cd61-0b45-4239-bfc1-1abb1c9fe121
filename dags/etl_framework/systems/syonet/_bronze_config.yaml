# Configuração Bronze - Sistema Syonet
# Substitui completamente tables_config_v3_migrated.py
# IMPORTANTE: Este arquivo centraliza TODA a configuração de tabelas bronze

system:
  name: "syonet"
  source_type: "sqlserver"
  description: "Sistema CRM Syonet - Dados de vendas, clientes e eventos"
  host: "***********"
  port: 50666
  database: "master"
  user: "bq_dwcorporativo_u"
  password: "N#OK+#{Yx*"
  default_timeout_seconds: 900
  max_parallel_tables: 2
  source_schema: "dbo"
  target_schema: "dbdwcorporativo"
  table_prefix: "bronze_syonet_"

# Filtros incrementais padrão do sistema
# IMPORTANTE: Estes são os filtros originais do V3 para uso no OPENQUERY
# O framework V4 usa DELETE baseado em IDs, não em filtros de data
incremental_filters:
  daily_openquery: |
    (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
     OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))
  
  seven_days_openquery: |
    (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
     OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')

# Lista completa de tabelas bronze (35 tabelas migradas do V3)
tables:
  # ===== TABELAS PEQUENAS (sempre full load) =====
  syo_agenda:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_contaemail:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_contatoemail:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_clientearea:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_telefones:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_novaclassificacao:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_novaclassificacaotipocliente:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_faixanovaclassificacao:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_etapafunil:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_empresa:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_empresausuario:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_donoconta:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_oficina:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_veiculo:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_dadosinterfacecliente:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_interfacenegociacao:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_campointerfacenegociacao:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_modeloversao:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  syo_motivoresultado:
    type: small
    mode: full_only
    timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10

  # ===== TABELAS COM CONFIGURAÇÕES ESPECIAIS =====
  
  campanhav2whatsapp:
    type: small
    mode: full_only
    source_table: "syo_campanhav2whatsapp"  # Nome diferente na origem
    timeout_seconds: 300
    
  syo_usuario:
    type: small
    mode: full_only
    timeout_seconds: 300
    select_fields: "id_usuario,nm_login,id_grupo,id_dominio,id_tipo,ap_usuario,nm_usuario,nm_sobrenome,ds_usuario,ds_email,ic_ativo,id_empresa,no_cgccpf"

  # ===== TABELAS GRANDES (incremental com fallback) =====
  
  syo_evento:
    type: large
    mode: smart_incremental
    id_field: "id_evento"
    timeout_seconds: 900
    select_fields: "id_evento, id_pesquisa, id_contato, id_estacao, id_empresa, id_cliente, nm_cliente, id_agente, id_ramal, id_statusevento, id_situacaoevento, id_prioridadeevento, id_grupoevento, id_tipoevento, id_componente, id_grupocampanha, id_email, ds_formacontato, ds_acaoevento, dt_proximaacao, ds_iniciativacontato, ds_assunto, ds_origem, dt_horainicio, dt_horafinal, dt_limite, substring(ds_conclusao,1,3800) as ds_conclusao, dt_conclusao, ds_palavrachave, dt_previsaoresposta, dt_previsaotermino, ic_primeiroatendimento, id_dealer, id_campanha, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt, no_nota, ds_observacao_json, ds_prisma, ds_midia, ds_pedidocompra_json, id_agenda, no_minuta, ds_valorinvestido, ds_tempoinvestido, no_horaproximaacao, id_equipesobdemanda, no_pedido, dt_periodobase, id_modulocriacaoevento, ic_geradoautomatico, ic_principal, id_eventoprincipal, ic_tempolimiteesgotado, ds_resultado, ic_reativar, dt_classificacaofrio, id_campanhav2, ds_temperatura, uuid_evento, dt_visita, dt_venda, id_agendamentoerp, ic_centralleads, dt_previsaoentrega, id_filarecepcao, ds_resumolead, dt_visitafrotista, id_atendente_atual, id_empresa_atual, id_usuario_atual, no_os"
    
  syo_cliente:
    type: large
    mode: smart_incremental
    id_field: "id_cliente"
    timeout_seconds: 900
    
  syo_acao:
    type: large
    mode: smart_incremental
    id_field: "id_acao"
    timeout_seconds: 900
    select_fields: "id_acao, id_evento, id_cliente, id_agente, tp_acao, ds_origem, ds_iniciativa, dh_acao, ds_resultado, CAST(LEFT(ds_conclusaoacao, 1000) AS VARCHAR(3900)) as ds_conclusaoacao, id_motivoresultado, ds_descricao, ic_confirmado, ds_temperatura, id_contato, ds_pendencia, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt, ds_agendamento, ds_respostasmsmo, id_email, id_sms, no_latitude, no_longitude, ic_mobile"
    
  syo_cidade:
    type: large
    mode: smart_incremental
    id_field: "id_cidade"
    timeout_seconds: 900
    
  syo_clientealteracao:
    type: large
    mode: smart_incremental
    id_field: "id_clientealteracao"
    timeout_seconds: 900
    
  syo_registrointerface:
    type: large
    mode: smart_incremental
    id_field: "id_registrointerface"
    timeout_seconds: 900
    
  syo_clientenovaclassificacao:
    type: large
    mode: smart_incremental
    id_field: "id_novaclassificacao"
    timeout_seconds: 900
    
  syo_historicoetapafunilevento:
    type: large
    mode: smart_incremental
    id_field: "id_historicoetapafunilevento"
    timeout_seconds: 900
    
  syo_donocontacliente:
    type: large
    mode: smart_incremental
    id_field: "id_donocontacliente"
    timeout_seconds: 900
    
  syo_peca:
    type: large
    mode: smart_incremental
    id_field: "no_controle"
    timeout_seconds: 900

  # ===== TABELA COM SQL CUSTOMIZADO (caso especial do V3) =====
  
  syo_encaminhamento:
    type: custom
    mode: smart_incremental
    id_field: "id_encaminhamento,id_evento"  # Chave composta
    timeout_seconds: 900
    custom_sql:
      incremental: |
        SELECT *
        FROM OPENQUERY(POSTGRES,
        '
        with
        base as (
            select distinct id_evento from public.syo_encaminhamento
            where (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                   OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))
        )
        select e.* from public.syo_encaminhamento e
        inner join base b on e.id_evento = b.id_evento
        ')
      full: |
        SELECT *
        FROM OPENQUERY(POSTGRES,
        '
        SELECT * FROM public.syo_encaminhamento
        ')

  syo_empresacliente:
    type: custom
    mode: smart_incremental
    id_field: "id_cliente,id_empresa"  # Chave composta
    timeout_seconds: 900
    max_gap_percentage: 99.0  # Permite gaps grandes (tabela com poucos updates diários)
    max_abs_diff: 1000000  # Permite diferenças absolutas grandes
    custom_sql:
      incremental: |
        SELECT *
        FROM OPENQUERY(POSTGRES, 'SELECT *
                                    FROM public.syo_empresacliente
                                    WHERE (dt_inc is not null  OR dt_alt is not null)
                                        AND (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                                             OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))')
      full: |
        SELECT * FROM OPENQUERY(POSTGRES, 'SELECT * FROM public.syo_empresacliente')

  syo_camposregistrointerface:
    type: large
    mode: smart_incremental
    id_field: "id_camposregistrointerface"
    timeout_seconds: 900
    select_fields: "id_camposregistrointerface, id_campointerfacenegociacao, id_registrointerface, ds_etiqueta, CAST(LEFT(ds_valor, 1000) AS VARCHAR(3900)) as ds_valor, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt"

  syo_evento_obs:
    type: custom
    mode: smart_incremental
    id_field: "id_evento"
    source_table: "syo_evento"
    timeout_seconds: 900
    custom_sql:
      full: |
        SELECT id_evento, dt_inc, ds_observacao
        FROM OPENQUERY(POSTGRES, 'SELECT id_evento, dt_inc, ds_observacao
                                  FROM public.syo_evento
                                  WHERE ds_observacao LIKE ''%Cadencia Meetime:%''')
      incremental: |
        SELECT id_evento, dt_inc, ds_observacao
        FROM OPENQUERY(POSTGRES,
            'SELECT id_evento, dt_inc, ds_observacao
             from public.syo_evento
             where (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                    OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))
                   and ds_observacao like ''%Cadencia Meetime:%''')