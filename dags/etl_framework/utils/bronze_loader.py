"""
Bronze Loader - ETL Framework

Utilitário para carregar configurações de tabelas bronze de arquivos YAML organizados por sistema.
Substitui arquivos Python hardcoded por configuração declarativa e sustentável.
"""

import os
import yaml
from typing import Dict, List, Optional
from pathlib import Path
from dataclasses import dataclass
from ..config.table_config import TableConfig, TableType, IncrementalMode
from ..config.database_config import DatabaseConfig, DatabaseType


@dataclass
class BronzeSystemConfig:
    """Configuração completa de um sistema bronze"""
    name: str
    source_type: str
    description: str
    host: str
    port: int
    database: str
    user: str
    password: str
    default_timeout_seconds: int
    max_parallel_tables: int
    source_schema: str
    target_schema: str
    table_prefix: str
    incremental_filters: Dict[str, str]
    tables: Dict[str, Dict]


class BronzeLoader:
    """
    Carregador de configurações bronze organizadas por sistema
    
    Estrutura esperada:
    systems/[system_name]/
    ├── _bronze_config.yaml     # Configuração completa do sistema
    └── config.py               # Configuração principal (carrega do YAML)
    """
    
    def __init__(self, base_path: str = None):
        if base_path is None:
            # Automaticamente detecta o caminho base do framework
            current_dir = Path(__file__).parent.parent
            self.base_path = current_dir / "systems"
        else:
            self.base_path = Path(base_path)
    
    def load_system_bronze_config(self, system_name: str) -> BronzeSystemConfig:
        """
        Carrega configuração bronze completa de um sistema
        
        Args:
            system_name: Nome do sistema (ex: 'syonet', 'oracle_erp')
            
        Returns:
            BronzeSystemConfig com todos os dados do sistema
        """
        system_path = self.base_path / system_name
        config_file = system_path / "_bronze_config.yaml"
        
        if not config_file.exists():
            raise FileNotFoundError(f"Arquivo _bronze_config.yaml não encontrado em {system_path}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            system_config = config_data.get('system', {})
            
            return BronzeSystemConfig(
                name=system_config.get('name', system_name),
                source_type=system_config.get('source_type', 'postgresql'),
                description=system_config.get('description', f'Sistema {system_name}'),
                host=system_config.get('host', 'localhost'),
                port=system_config.get('port', 5432),
                database=system_config.get('database', 'database'),
                user=system_config.get('user', 'user'),
                password=system_config.get('password', 'password'),
                default_timeout_seconds=system_config.get('default_timeout_seconds', 600),
                max_parallel_tables=system_config.get('max_parallel_tables', 3),
                source_schema=system_config.get('source_schema', 'public'),
                target_schema=system_config.get('target_schema', 'bronze'),
                table_prefix=system_config.get('table_prefix', f'bronze_{system_name}_'),
                incremental_filters=config_data.get('incremental_filters', {}),
                tables=config_data.get('tables', {})
            )
            
        except Exception as e:
            raise ValueError(f"Erro ao carregar _bronze_config.yaml do sistema {system_name}: {e}")
    
    def create_database_config(self, system_config: BronzeSystemConfig) -> DatabaseConfig:
        """
        Cria DatabaseConfig a partir da configuração bronze
        
        Args:
            system_config: Configuração bronze do sistema
            
        Returns:
            DatabaseConfig configurado
        """
        # Mapeia string para enum
        db_type_map = {
            'sqlserver': DatabaseType.SQLSERVER,
            'oracle': DatabaseType.ORACLE,
            'postgresql': DatabaseType.POSTGRESQL
        }
        
        db_type = db_type_map.get(
            system_config.source_type.lower(),
            DatabaseType.POSTGRESQL
        )
        
        return DatabaseConfig(
            name=f"{system_config.name}_source",
            db_type=db_type,
            host=system_config.host,
            port=system_config.port,
            database=system_config.database,
            user=system_config.user,
            password=system_config.password,
            timeout=system_config.default_timeout_seconds
        )
    
    def create_table_configs(self, system_config: BronzeSystemConfig) -> List[TableConfig]:
        """
        Cria lista de TableConfig a partir da configuração bronze
        
        Args:
            system_config: Configuração bronze do sistema
            
        Returns:
            Lista de TableConfig configuradas
        """
        table_configs = []
        
        # Mapeia strings para enums
        type_map = {
            'small': TableType.SMALL,
            'large': TableType.LARGE,
            'custom': TableType.CUSTOM
        }
        
        mode_map = {
            'full_only': IncrementalMode.FULL_ONLY,
            'smart_incremental': IncrementalMode.SMART,
            'daily': IncrementalMode.DAILY,
            'seven_days': IncrementalMode.SEVEN_DAYS
        }
        
        for table_name, table_config in system_config.tables.items():
            # Mapeia configuração
            table_type = type_map.get(table_config.get('type', 'small'), TableType.SMALL)
            incremental_mode = mode_map.get(table_config.get('mode', 'full_only'), IncrementalMode.FULL_ONLY)
            
            # Cria configuração da tabela
            config = TableConfig(
                name=table_name,
                table_type=table_type,
                incremental_mode=incremental_mode,
                id_field=table_config.get('id_field'),
                source_table=table_config.get('source_table'),  # Nome diferente na origem
                select_fields=table_config.get('select_fields'),  # SELECT customizado
                source_schema=system_config.source_schema,
                target_schema=system_config.target_schema,
                table_prefix=system_config.table_prefix,
                max_gap_percentage=table_config.get('max_gap_percentage', 20.0),
                max_abs_diff=table_config.get('max_abs_diff', 1000),
                timeout_seconds=table_config.get('timeout_seconds', system_config.default_timeout_seconds),
                custom_sql=table_config.get('custom_sql'),  # SQL customizado para casos especiais
                daily_filter=None,  # Framework V4 usa DELETE baseado em IDs, não filtros de data
                seven_days_filter=None  # Framework V4 usa DELETE baseado em IDs, não filtros de data
            )
            
            table_configs.append(config)
        
        return table_configs
    
    def _resolve_filter_condition(self, filter_name: str, incremental_filters: Dict[str, str]) -> Optional[str]:
        """
        Resolve nome do filtro para SQL real
        
        Args:
            filter_name: Nome do filtro (ex: 'daily', 'seven_days')
            incremental_filters: Dicionário de filtros disponíveis
            
        Returns:
            SQL do filtro ou None se não encontrado
        """
        if not filter_name:
            return None
            
        return incremental_filters.get(filter_name)
    
    def create_system_tables_from_yaml(self, system_name: str) -> List[TableConfig]:
        """
        Função principal: Carrega todas as tabelas de um sistema do YAML
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            Lista de TableConfig prontas para uso
        """
        # Carrega configuração do sistema
        system_config = self.load_system_bronze_config(system_name)
        
        # Cria configurações de tabelas
        table_configs = self.create_table_configs(system_config)
        
        return table_configs
    
    def get_available_systems(self) -> List[str]:
        """
        Retorna lista de sistemas disponíveis (que têm _bronze_config.yaml)
        
        Returns:
            Lista de nomes de sistemas
        """
        systems = []
        
        if not self.base_path.exists():
            return systems
        
        for system_dir in self.base_path.iterdir():
            if system_dir.is_dir():
                config_file = system_dir / "_bronze_config.yaml"
                if config_file.exists():
                    systems.append(system_dir.name)
        
        return sorted(systems)


def create_system_bronze_structure(system_name: str, base_path: str = None) -> str:
    """
    Cria estrutura bronze para um novo sistema
    
    Args:
        system_name: Nome do sistema
        base_path: Caminho base (opcional)
        
    Returns:
        Caminho da pasta do sistema criada
    """
    if base_path is None:
        current_dir = Path(__file__).parent.parent
        base_path = current_dir / "systems"
    else:
        base_path = Path(base_path)
    
    system_path = base_path / system_name
    
    # Cria diretório se não existir
    system_path.mkdir(parents=True, exist_ok=True)
    
    # Cria template de _bronze_config.yaml se não existir
    config_file = system_path / "_bronze_config.yaml"
    if not config_file.exists():
        config_template = f"""# Configuração Bronze - Sistema {system_name}
# Centraliza TODA a configuração de tabelas bronze do sistema

system:
  name: "{system_name}"
  source_type: "postgresql"  # sqlserver|oracle|postgresql
  description: "Sistema {system_name} -> Corporate DW"
  host: "localhost"
  port: 5432
  database: "database"
  user: "user"
  password: "password"
  default_timeout_seconds: 600
  max_parallel_tables: 3
  source_schema: "public"
  target_schema: "bronze"
  table_prefix: "bronze_{system_name}_"

# Filtros incrementais padrão do sistema
incremental_filters:
  daily: |
    (created_date >= DATE_TRUNC('day', CURRENT_TIMESTAMP)
     OR updated_date >= DATE_TRUNC('day', CURRENT_TIMESTAMP))
  
  seven_days: |
    (created_date >= CURRENT_TIMESTAMP - INTERVAL '7 days'
     OR updated_date >= CURRENT_TIMESTAMP - INTERVAL '7 days')

# Lista de tabelas bronze
tables:
  # Exemplo de tabela pequena (full load sempre)
  # exemplo_small_table:
  #   type: small
  #   mode: full_only
  #   timeout_seconds: 300
  
  # Exemplo de tabela grande (incremental inteligente)
  # exemplo_large_table:
  #   type: large
  #   mode: smart_incremental
  #   id_field: "id"
  #   timeout_seconds: 900
  #   filter: daily  # Usa filtro 'daily' definido acima
"""
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(config_template)
    
    return str(system_path)