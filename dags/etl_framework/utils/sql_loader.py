"""
SQL Loader - ETL Framework

Utilitário para carregar consultas SQL de arquivos organizados por sistema.
Substitui SQL hardcoded por arquivos organizados e sustentáveis.
"""

import os
import yaml
from typing import Dict, List, Optional
from pathlib import Path
from dataclasses import dataclass
from ..c2_silver.transformation import SilverTransformation, TransformationFrequency


@dataclass
class SQLTransformationConfig:
    """Configuração de uma transformação SQL"""
    name: str
    dependencies: List[str]
    frequency: str = "daily"
    timeout_seconds: int = 600
    description: Optional[str] = None
    grants: List[str] = None
    tags: List[str] = None


class SQLLoader:
    """
    Carregador de arquivos SQL organizados por sistema
    
    Estrutura esperada:
    systems/[system_name]/sql/
    ├── dependencies.yaml
    ├── transformation1.sql
    ├── transformation2.sql
    └── ...
    """
    
    def __init__(self, base_path: str = None):
        if base_path is None:
            # Automaticamente detecta o caminho base do framework
            current_dir = Path(__file__).parent.parent
            self.base_path = current_dir / "systems"
        else:
            self.base_path = Path(base_path)
    
    def load_system_transformations(self, system_name: str) -> List[SilverTransformation]:
        """
        Carrega todas as transformações SQL de um sistema
        
        Args:
            system_name: Nome do sistema (ex: 'syonet', 'oracle_erp')
            
        Returns:
            Lista de SilverTransformation configuradas
        """
        system_path = self.base_path / system_name
        sql_path = system_path / "sql"
        
        if not sql_path.exists():
            return []
        
        # Carrega configuração silver (apenas dependências entre silvers)
        silver_config = self._load_silver_config(sql_path)
        
        # Carrega arquivos SQL
        transformations = []
        for sql_file in sql_path.glob("*.sql"):
            transformation_name = sql_file.stem
            
            # Pula arquivos que começam com underscore (utilitários)
            if transformation_name.startswith('_'):
                continue
                
            sql_content = self._load_sql_file(sql_file)
            config = silver_config.get(transformation_name, {})
            
            transformation = self._create_transformation(
                name=transformation_name,
                sql=sql_content,
                config=config,
                system_name=system_name
            )
            
            transformations.append(transformation)
        
        # Ordena por dependências silver (bronze é implícita)
        return self._sort_by_silver_dependencies(transformations)
    
    def _load_silver_config(self, sql_path: Path) -> Dict:
        """Carrega arquivo de configuração silver (apenas dependências entre silvers)"""
        config_file = sql_path / "_silver_config.yaml"
        
        if not config_file.exists():
            return {}
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                return config.get('transformations', {})
        except Exception as e:
            print(f"Erro ao carregar _silver_config.yaml: {e}")
            return {}
    
    def _load_sql_file(self, sql_file: Path) -> str:
        """Carrega conteúdo de um arquivo SQL"""
        try:
            with open(sql_file, 'r', encoding='utf-8') as f:
                return f.read().strip()
        except Exception as e:
            raise ValueError(f"Erro ao carregar {sql_file}: {e}")
    
    def _create_transformation(self, name: str, sql: str, config: Dict, system_name: str = None) -> SilverTransformation:
        """Cria SilverTransformation a partir da configuração"""
        
        # Mapeia frequency string para enum
        frequency_map = {
            'daily': TransformationFrequency.DAILY,
            'weekly': TransformationFrequency.WEEKLY,
            'monthly': TransformationFrequency.MONTHLY,
            'on_demand': TransformationFrequency.ON_DEMAND
        }
        
        frequency = frequency_map.get(
            config.get('frequency', 'daily'),
            TransformationFrequency.DAILY
        )
        
        # 🆕 NOVO: Gera CREATE TABLE automaticamente
        full_sql = self._generate_create_table_sql(
            system_name=system_name or 'unknown',
            table_name=name,
            select_sql=sql,
            config=config
        )
        
        return SilverTransformation(
            name=name,
            description=config.get('description', f'Transformação {name}'),
            sql=full_sql,  # 🆕 Usa SQL completo gerado
            frequency=frequency,
            timeout_seconds=config.get('timeout_seconds', 600),
            dependencies=config.get('dependencies', []),
            grants=config.get('grants', []),
            tags=config.get('tags', []),
            owner=config.get('owner', 'data-engineering'),
            drop_if_exists=config.get('drop_if_exists', True)
        )
    
    def _sort_by_silver_dependencies(self, transformations: List[SilverTransformation]) -> List[SilverTransformation]:
        """
        Ordena transformações respeitando APENAS dependências entre silvers
        
        Dependências bronze são implícitas (silver sempre roda após bronze).
        Apenas dependências entre transformações silver são consideradas.
        """
        sorted_transformations = []
        remaining = transformations.copy()
        transformation_names = {t.name for t in transformations}
        
        while remaining:
            # Encontra transformações que podem ser executadas agora
            ready = []
            for transformation in remaining:
                # APENAS dependências silver são verificadas
                # (dependências bronze são ignoradas pois são implícitas)
                silver_deps = [
                    dep for dep in transformation.dependencies 
                    if dep in transformation_names  # Só outras transformações silver
                ]
                
                if all(dep in [t.name for t in sorted_transformations] for dep in silver_deps):
                    ready.append(transformation)
            
            if not ready:
                # Dependência circular ou erro - adiciona o restante
                print(f"Possível dependência circular detectada. Restantes: {[t.name for t in remaining]}")
                sorted_transformations.extend(remaining)
                break
            
            # Adiciona as prontas e remove da lista
            sorted_transformations.extend(ready)
            for t in ready:
                remaining.remove(t)
        
        return sorted_transformations
    
    def _generate_create_table_sql(self, system_name: str, table_name: str, select_sql: str, config: Dict) -> str:
        """
        Gera SQL completo com CREATE TABLE a partir da consulta SELECT
        
        Args:
            system_name: Nome do sistema (ex: 'syonet')
            table_name: Nome da transformação (ex: 'tb_oportunidades_base')
            select_sql: Consulta SELECT pura
            config: Configuração da transformação
            
        Returns:
            SQL completo com CREATE TABLE, DROP IF EXISTS e GRANTS
        """
        # Gera nome completo da tabela
        full_table_name = f"dbdwcorporativo.silver_{system_name}_{table_name}"
        
        sql_parts = []
        
        # 1. DROP IF EXISTS (se configurado)
        if config.get('drop_if_exists', True):
            sql_parts.append(f"DROP TABLE IF EXISTS {full_table_name};")
        
        # 2. CREATE TABLE AS
        sql_parts.append(f"CREATE TABLE {full_table_name} AS")
        
        # 3. Consulta SELECT (remove comentários do início para o CREATE TABLE)
        clean_select = self._clean_select_sql(select_sql)
        sql_parts.append(clean_select + ";")
        
        # 4. GRANTS são tratados pelo SilverProcessor, não incluímos no SQL
        # Os grants ficam no objeto SilverTransformation.grants
        
        return "\n".join(sql_parts)
    
    def _clean_select_sql(self, select_sql: str) -> str:
        """
        Remove comentários do início do SQL para usar no CREATE TABLE
        
        Args:
            select_sql: SQL com possíveis comentários no início
            
        Returns:
            SQL limpo começando com WITH ou SELECT
        """
        lines = select_sql.strip().split('\n')
        
        # Encontra a primeira linha que não é comentário
        start_index = 0
        for i, line in enumerate(lines):
            stripped = line.strip()
            if stripped and not stripped.startswith('--'):
                start_index = i
                break
        
        # Retorna SQL limpo
        clean_lines = lines[start_index:]
        return '\n'.join(clean_lines)
    
    def create_sql_template(self, system_name: str, transformation_name: str) -> str:
        """Cria template SQL para nova transformação"""
        return f"""-- Transformação: {transformation_name}
-- Sistema: {system_name}
-- Descrição: [Descrever o que esta transformação faz]

CREATE TABLE dbdwcorporativo.silver_{system_name}_{transformation_name} AS
SELECT
    -- Adicionar colunas aqui
    *
FROM dbdwcorporativo.bronze_{system_name}_[tabela_origem]
WHERE 1=1
    -- Adicionar filtros aqui
;

-- Grants padrão
GRANT SELECT ON TABLE dbdwcorporativo.silver_{system_name}_{transformation_name} TO bq_vitor_barros_u;
"""


def create_system_sql_structure(system_name: str, base_path: str = None) -> str:
    """
    Cria estrutura de pastas SQL para um novo sistema
    
    Args:
        system_name: Nome do sistema
        base_path: Caminho base (opcional)
        
    Returns:
        Caminho da pasta SQL criada
    """
    if base_path is None:
        current_dir = Path(__file__).parent.parent
        base_path = current_dir / "systems"
    else:
        base_path = Path(base_path)
    
    system_path = base_path / system_name
    sql_path = system_path / "sql"
    
    # Cria diretório se não existir
    sql_path.mkdir(parents=True, exist_ok=True)
    
    # Cria arquivo __init__.py
    init_file = sql_path / "__init__.py"
    if not init_file.exists():
        with open(init_file, 'w', encoding='utf-8') as f:
            f.write(f'"""SQL transformations for {system_name} system"""\n')
    
    # Cria template de _silver_config.yaml se não existir
    deps_file = sql_path / "_silver_config.yaml"
    if not deps_file.exists():
        deps_template = f"""# Configuração Silver - Sistema {system_name}
# 
# IMPORTANTE: Apenas dependências entre transformações SILVER são necessárias.
# Dependências BRONZE são implícitas (silver sempre roda após bronze).
# 
# Estrutura:
# transformations:
#   nome_transformacao:
#     dependencies: [lista_de_outras_silvers]  # APENAS silvers!
#     frequency: daily|weekly|monthly|on_demand
#     timeout_seconds: 600
#     description: "Descrição da transformação"
#     tags: [lista_de_tags]

transformations:
  # Exemplo - transformação base (sem dependências silver):
  # base_transformacao:
  #   frequency: daily
  #   timeout_seconds: 300
  #   description: "Transformação base"
  #   tags: ["{system_name}", "base"]
  #
  # Exemplo - transformação dependente:
  # derived_transformacao:
  #   dependencies: [base_transformacao]  # Depende da silver acima
  #   frequency: daily
  #   timeout_seconds: 300
  #   description: "Transformação derivada"
  #   tags: ["{system_name}", "derived"]
"""
        with open(deps_file, 'w', encoding='utf-8') as f:
            f.write(deps_template)
    
    return str(sql_path)